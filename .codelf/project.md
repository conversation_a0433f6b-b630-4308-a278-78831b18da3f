## Codelf

> 项目使用的技术、工具库及对应依赖版本如下：
> TypeScript（v5.7.3）、Node.js、@modelcontextprotocol/sdk（v1.5.0）、Zod（v3.24.2）

## 项目结构

> 文件级别的分析对于理解项目至关重要。

> 以下是项目的目录结构，并对重要部分进行了注释说明。

root

- .codelf // 项目文档目录，存放项目相关的说明文档
  ├── steering/ // Steering 文档目录，持久化项目知识
  │ ├── product.md // 产品概述文档
  │ ├── tech.md // 技术栈文档
  │ └── structure.md // 项目结构文档
  ├── workflows/ // 工作流程目录
  │ ├── specs/ // 规范工作流目录
  │ ├── bugs/ // Bug 修复工作流目录
  │ └── config.json // 工作流程配置文件
  ├── templates/ // 模板文件目录
  └── \*.md // 项目文档文件
- .git // Git 版本控制目录
- .gitignore // Git 忽略文件配置
- index.ts // 主入口文件，包含 MCP 服务器实现和工具函数定义
- node_modules // Node.js 依赖包目录
- package.json // 项目配置文件，定义依赖和脚本
- pnpm-lock.yaml // pnpm 包管理器锁定文件
- README.md // 英文版项目说明文档
- README_CN.md // 中文版项目说明文档
- tsconfig.json // TypeScript 配置文件
- WORKFLOW_README.md // 工作流程说明文档

### 核心功能

该项目是一个 MCP（Model Context Protocol）服务器实现，现已扩展为完整的自动化工作流程系统，提供以下工具功能：

#### 基础工具

1. `get-project-info` - 获取项目详细信息，帮助 AI 更好地理解代码
2. `update-project-info` - 更新项目信息，维护.codelf 目录下的文档
3. `init-codelf` - 初始化.codelf 目录和文件，帮助建立项目文档结构

#### Steering 系统（持久化项目知识）

4. `init-steering` - 初始化 Steering 文档系统，创建产品概述、技术栈和项目结构文档
5. `get-steering` - 获取 Steering 文档内容，用于指导工作流程

#### 规范工作流（Spec Workflow）

6. `spec-create` - 创建新的规范工作流
7. `spec-requirements` - 为当前规范生成需求文档
8. `spec-status` - 查看规范的当前状态
9. `spec-list` - 列出所有规范

#### Bug 修复工作流（Bug Fix Workflow）

10. `bug-create` - 创建新的 Bug 修复工作流
11. `bug-status` - 查看 Bug 的当前状态

### 工作流程特性

#### 🎯 规范驱动开发流程

- **需求分析** → **设计文档** → **任务分解** → **实施执行**
- 集成 Steering 文档确保项目标准一致性
- 自动生成结构化文档和模板

#### 🐛 Bug 修复工作流程

- **问题报告** → **根因分析** → **修复实施** → **验证测试**
- 支持严重程度分级（low/medium/high/critical）
- 系统化的问题跟踪和状态管理

#### 📋 Steering 文档系统

- **product.md** - 产品愿景、目标用户、核心功能
- **tech.md** - 技术栈、开发工具、技术约束
- **structure.md** - 文件组织、命名约定、代码组织原则

项目通过 Node.js 实现，使用 TypeScript 进行开发，主要面向 AI 代码助手提供上下文信息和自动化工作流程支持。
